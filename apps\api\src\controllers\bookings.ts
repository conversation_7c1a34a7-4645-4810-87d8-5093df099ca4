import { Request, Response } from 'express';
import { supabaseAdmin } from '@freela/database/src/supabase';
import { logger } from '../utils/logger';
import { AppError } from '../utils/errors';

// Booking data mapping utilities
const mapToSupabaseBooking = (data: any) => {
  const mapped: any = {};
  
  for (const [key, value] of Object.entries(data)) {
    switch (key) {
      case 'serviceId':
        mapped.service_id = value;
        break;
      case 'clientId':
        mapped.client_id = value;
        break;
      case 'expertId':
        mapped.expert_id = value;
        break;
      case 'finalPrice':
        mapped.final_price = value;
        break;
      case 'estimatedDelivery':
        mapped.estimated_delivery = value;
        break;
      case 'contactPreference':
        mapped.contact_preference = value;
        break;
      case 'createdAt':
        mapped.created_at = value;
        break;
      case 'updatedAt':
        mapped.updated_at = value;
        break;
      default:
        mapped[key] = value;
    }
  }
  
  return mapped;
};

const mapFromSupabaseBooking = (data: any) => {
  if (!data) return data;
  
  const mapped: any = {};
  
  for (const [key, value] of Object.entries(data)) {
    switch (key) {
      case 'service_id':
        mapped.serviceId = value;
        break;
      case 'client_id':
        mapped.clientId = value;
        break;
      case 'expert_id':
        mapped.expertId = value;
        break;
      case 'final_price':
        mapped.finalPrice = value;
        break;
      case 'estimated_delivery':
        mapped.estimatedDelivery = value;
        break;
      case 'contact_preference':
        mapped.contactPreference = value;
        break;
      case 'created_at':
        mapped.createdAt = value;
        break;
      case 'updated_at':
        mapped.updatedAt = value;
        break;
      default:
        mapped[key] = value;
    }
  }
  
  return mapped;
};

/**
 * Create a new booking request
 */
export const createBooking = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    const { serviceId, ...bookingData } = req.body;

    // Get service details and expert info
    const { data: service, error: serviceError } = await supabaseAdmin
      .from('services')
      .select(`
        id,
        expert_id,
        title,
        base_price,
        is_active,
        expert_profiles!inner(
          id,
          user_id,
          users!inner(
            id,
            first_name,
            last_name,
            email
          )
        )
      `)
      .eq('id', serviceId)
      .eq('is_active', true)
      .single();

    if (serviceError || !service) {
      throw new AppError('Service not found or not available', 404);
    }

    // Check if user is trying to book their own service
    if ((service as any).expert_profiles.user_id === userId) {
      throw new AppError('You cannot book your own service', 400);
    }

    // Check if there's already a pending booking for this service by this client
    const { data: existingBooking, error: existingError } = await supabaseAdmin
      .from('bookings')
      .select('id')
      .eq('service_id', serviceId)
      .eq('client_id', userId)
      .in('status', ['PENDING', 'ACCEPTED', 'IN_PROGRESS'])
      .single();

    if (existingBooking) {
      throw new AppError('You already have an active booking for this service', 400);
    }

    const newBooking = {
      ...bookingData,
      serviceId,
      clientId: userId,
      expertId: service.expert_id,
      status: 'PENDING',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const supabaseData = mapToSupabaseBooking(newBooking);

    const { data: booking, error } = await supabaseAdmin
      .from('bookings')
      .insert(supabaseData)
      .select(`
        *,
        services!inner(
          id,
          title,
          base_price,
          expert_profiles!inner(
            id,
            users!inner(
              id,
              first_name,
              last_name,
              email
            )
          )
        ),
        users!client_id(
          id,
          first_name,
          last_name,
          email
        )
      `)
      .single();

    if (error) {
      logger.error('Error creating booking:', error);
      throw new AppError('Failed to create booking', 500);
    }

    const mappedBooking = mapFromSupabaseBooking(booking);

    logger.info('Booking created successfully', { 
      bookingId: booking.id, 
      serviceId,
      clientId: userId,
      expertId: service.expert_id
    });

    res.status(201).json({
      success: true,
      message: 'Booking request created successfully',
      data: mappedBooking
    });
  } catch (error) {
    logger.error('Create booking error:', error);
    
    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        type: error.type
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      type: 'INTERNAL_ERROR'
    });
  }
};

/**
 * Get bookings with filters and pagination
 */
export const getBookings = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const userRole = req.user?.role;
    
    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    const {
      status,
      serviceId,
      expertId,
      clientId,
      urgency,
      fromDate,
      toDate,
      sortBy = 'created_at',
      sortOrder = 'desc',
      page = 1,
      limit = 20
    } = req.query;

    let query = supabaseAdmin
      .from('bookings')
      .select(`
        *,
        services!inner(
          id,
          title,
          base_price,
          expert_profiles!inner(
            id,
            users!inner(
              id,
              first_name,
              last_name,
              email
            )
          )
        ),
        users!client_id(
          id,
          first_name,
          last_name,
          email
        )
      `, { count: 'exact' });

    // Role-based filtering
    if (userRole === 'CLIENT') {
      query = query.eq('client_id', userId);
    } else if (userRole === 'EXPERT') {
      query = query.eq('expert_id', expertId || userId);
    }
    // Admin can see all bookings

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }

    if (serviceId) {
      query = query.eq('service_id', serviceId);
    }

    if (expertId && userRole === 'ADMIN') {
      query = query.eq('expert_id', expertId);
    }

    if (clientId && userRole === 'ADMIN') {
      query = query.eq('client_id', clientId);
    }

    if (urgency) {
      query = query.eq('urgency', urgency);
    }

    if (fromDate) {
      query = query.gte('created_at', fromDate);
    }

    if (toDate) {
      query = query.lte('created_at', toDate);
    }

    // Sorting
    const sortField = sortBy === 'deadline' ? 'deadline' :
                     sortBy === 'budget' ? 'budget' :
                     sortBy === 'status' ? 'status' :
                     'created_at';
    
    query = query.order(sortField, { ascending: sortOrder === 'asc' });

    // Pagination
    const offset = (Number(page) - 1) * Number(limit);
    query = query.range(offset, offset + Number(limit) - 1);

    const { data: bookings, error, count } = await query;

    if (error) {
      logger.error('Error getting bookings:', error);
      throw new AppError('Failed to retrieve bookings', 500);
    }

    const mappedBookings = bookings?.map(mapFromSupabaseBooking) || [];

    const totalPages = Math.ceil((count || 0) / Number(limit));

    res.json({
      success: true,
      message: 'Bookings retrieved successfully',
      data: {
        bookings: mappedBookings,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: count || 0,
          totalPages
        }
      }
    });
  } catch (error) {
    logger.error('Get bookings error:', error);
    
    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        type: error.type
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      code: 'INTERNAL_ERROR'
    });
  }
};

/**
 * Get booking by ID
 */
export const getBookingById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    const { data: booking, error } = await supabaseAdmin
      .from('bookings')
      .select(`
        *,
        services!inner(
          id,
          title,
          base_price,
          expert_profiles!inner(
            id,
            users!inner(
              id,
              first_name,
              last_name,
              email
            )
          )
        ),
        users!client_id(
          id,
          first_name,
          last_name,
          email
        )
      `)
      .eq('id', id)
      .single();

    if (error || !booking) {
      throw new AppError('Booking not found', 404);
    }

    // Check access permissions
    const isClient = booking.client_id === userId;
    const isExpert = booking.services.expert_profiles.users.id === userId;
    const isAdmin = userRole === 'ADMIN';

    if (!isClient && !isExpert && !isAdmin) {
      throw new AppError('Access denied - you can only view your own bookings', 403);
    }

    const mappedBooking = mapFromSupabaseBooking(booking);

    res.json({
      success: true,
      message: 'Booking retrieved successfully',
      data: mappedBooking
    });
  } catch (error) {
    logger.error('Get booking by ID error:', error);

    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        type: error.type
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      type: 'INTERNAL_ERROR'
    });
  }
};

/**
 * Update booking status
 */
export const updateBookingStatus = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { status, message, estimatedDelivery, finalPrice } = req.body;
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    // Get the booking to check permissions and current status
    const { data: existingBooking, error: fetchError } = await supabaseAdmin
      .from('bookings')
      .select(`
        *,
        services!inner(
          expert_profiles!inner(
            user_id
          )
        )
      `)
      .eq('id', id)
      .single();

    if (fetchError || !existingBooking) {
      throw new AppError('Booking not found', 404);
    }

    // Check permissions based on status change
    const isClient = existingBooking.client_id === userId;
    const isExpert = existingBooking.services.expert_profiles.user_id === userId;
    const isAdmin = userRole === 'ADMIN';

    // Define allowed status transitions
    const allowedTransitions: { [key: string]: string[] } = {
      'PENDING': ['ACCEPTED', 'REJECTED'],
      'ACCEPTED': ['IN_PROGRESS', 'CANCELLED'],
      'IN_PROGRESS': ['COMPLETED', 'CANCELLED'],
      'REJECTED': [],
      'COMPLETED': [],
      'CANCELLED': []
    };

    // Check if status transition is allowed
    if (!allowedTransitions[existingBooking.status]?.includes(status)) {
      throw new AppError(`Cannot change status from ${existingBooking.status} to ${status}`, 400);
    }

    // Check role permissions for status changes
    if (status === 'ACCEPTED' || status === 'REJECTED' || status === 'IN_PROGRESS') {
      if (!isExpert && !isAdmin) {
        throw new AppError('Only the expert can accept, reject, or start work on bookings', 403);
      }
    } else if (status === 'CANCELLED') {
      if (!isClient && !isExpert && !isAdmin) {
        throw new AppError('Only the client or expert can cancel bookings', 403);
      }
    } else if (status === 'COMPLETED') {
      if (!isExpert && !isAdmin) {
        throw new AppError('Only the expert can mark bookings as completed', 403);
      }
    }

    const updateData = {
      status,
      message,
      estimatedDelivery,
      finalPrice,
      updatedAt: new Date().toISOString()
    };

    const supabaseData = mapToSupabaseBooking(updateData);

    const { data: updatedBooking, error } = await supabaseAdmin
      .from('bookings')
      .update(supabaseData)
      .eq('id', id)
      .select(`
        *,
        services!inner(
          id,
          title,
          base_price,
          expert_profiles!inner(
            id,
            users!inner(
              id,
              first_name,
              last_name,
              email
            )
          )
        ),
        users!client_id(
          id,
          first_name,
          last_name,
          email
        )
      `)
      .single();

    if (error) {
      logger.error('Error updating booking status:', error);
      throw new AppError('Failed to update booking status', 500);
    }

    const mappedBooking = mapFromSupabaseBooking(updatedBooking);

    logger.info('Booking status updated successfully', {
      bookingId: id,
      oldStatus: existingBooking.status,
      newStatus: status,
      userId
    });

    res.json({
      success: true,
      message: `Booking status updated to ${status}`,
      data: mappedBooking
    });
  } catch (error) {
    logger.error('Update booking status error:', error);

    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        type: error.type
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      type: 'INTERNAL_ERROR'
    });
  }
};

/**
 * Get current user's bookings as client
 */
export const getMyClientBookings = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    const {
      status,
      page = 1,
      limit = 20
    } = req.query;

    let query = supabaseAdmin
      .from('bookings')
      .select(`
        *,
        services!inner(
          id,
          title,
          base_price,
          expert_profiles!inner(
            id,
            users!inner(
              id,
              first_name,
              last_name,
              email
            )
          )
        )
      `, { count: 'exact' })
      .eq('client_id', userId);

    if (status) {
      query = query.eq('status', status);
    }

    // Pagination
    const offset = (Number(page) - 1) * Number(limit);
    query = query.range(offset, offset + Number(limit) - 1);

    // Order by creation date (newest first)
    query = query.order('created_at', { ascending: false });

    const { data: bookings, error, count } = await query;

    if (error) {
      logger.error('Error getting client bookings:', error);
      throw new AppError('Failed to retrieve client bookings', 500);
    }

    const mappedBookings = bookings?.map(mapFromSupabaseBooking) || [];

    const totalPages = Math.ceil((count || 0) / Number(limit));

    res.json({
      success: true,
      message: 'Client bookings retrieved successfully',
      data: {
        bookings: mappedBookings,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: count || 0,
          totalPages
        }
      }
    });
  } catch (error) {
    logger.error('Get my client bookings error:', error);

    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        type: error.type
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      type: 'INTERNAL_ERROR'
    });
  }
};

/**
 * Get current user's bookings as expert
 */
export const getMyExpertBookings = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    // First get the expert profile ID
    const { data: expertProfile, error: expertError } = await supabaseAdmin
      .from('expert_profiles')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (expertError || !expertProfile) {
      throw new AppError('Expert profile not found', 404);
    }

    const {
      status,
      page = 1,
      limit = 20
    } = req.query;

    let query = supabaseAdmin
      .from('bookings')
      .select(`
        *,
        services!inner(
          id,
          title,
          base_price
        ),
        users!client_id(
          id,
          first_name,
          last_name,
          email
        )
      `, { count: 'exact' })
      .eq('expert_id', expertProfile.id);

    if (status) {
      query = query.eq('status', status);
    }

    // Pagination
    const offset = (Number(page) - 1) * Number(limit);
    query = query.range(offset, offset + Number(limit) - 1);

    // Order by creation date (newest first)
    query = query.order('created_at', { ascending: false });

    const { data: bookings, error, count } = await query;

    if (error) {
      logger.error('Error getting expert bookings:', error);
      throw new AppError('Failed to retrieve expert bookings', 500);
    }

    const mappedBookings = bookings?.map(mapFromSupabaseBooking) || [];

    const totalPages = Math.ceil((count || 0) / Number(limit));

    res.json({
      success: true,
      message: 'Expert bookings retrieved successfully',
      data: {
        bookings: mappedBookings,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: count || 0,
          totalPages
        }
      }
    });
  } catch (error) {
    logger.error('Get my expert bookings error:', error);

    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        type: error.type
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      type: 'INTERNAL_ERROR'
    });
  }
};
